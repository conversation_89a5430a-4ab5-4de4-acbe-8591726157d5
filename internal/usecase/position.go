package usecase

import (
	"context"
	"fmt"
	"log"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/utils"
	"futures-asset/util"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	cfg "yt.com/backend/common.git/config"
)

// PositionUseCase 仓位用例实现
type PositionUseCase struct {
	config       *cfg.Config[configs.Config]
	positionRepo repository.PositionRepository
	cacheRepo    repository.CacheRepository
}

// PositionUseCaseParam 仓位用例参数
type PositionUseCaseParam struct {
	dig.In

	Config       *cfg.Config[configs.Config] `name:"config"`
	PositionRepo repository.PositionRepository
	CacheRepo    repository.CacheRepository
}

// NewPositionUseCase 创建仓位用例实例
func NewPositionUseCase(param PositionUseCaseParam) usecase.PositionUseCase {
	return &PositionUseCase{
		config:       param.Config,
		positionRepo: param.PositionRepo,
		cacheRepo:    param.CacheRepo,
	}
}

// PlatPosDetail implements usecase.PositionUseCase.
func (uc *PositionUseCase) PlatPosDetail(ctx context.Context, req *repository.PlatPosDetailReq) (repository.PlatPosDetail, error) {
	return uc.positionRepo.PlatPosDetail(ctx, req)
}

// PlatPosList implements usecase.PositionUseCase.
func (uc *PositionUseCase) PlatPosList(ctx context.Context) (repository.PlatPosList, error) {
	return uc.positionRepo.PlatPosList(ctx)
}

// PosInfo implements usecase.PositionUseCase.
func (uc *PositionUseCase) PosInfo(ctx context.Context, param *repository.UserPosParam) (repository.PosQuery, error) {
	return uc.positionRepo.PosInfo(ctx, param)
}

// PosTotal implements usecase.PositionUseCase.
func (uc *PositionUseCase) PosTotal(ctx context.Context, contractCode string) decimal.Decimal {
	return uc.positionRepo.PosTotal(ctx, contractCode)
}

// QueryUserPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) QueryUserPos(ctx context.Context, req *repository.UserPosParam) (repository.UserPosReply, error) {
	return uc.positionRepo.QueryUserPos(ctx, req)
}

// UserHoldPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) UserHoldPos(ctx context.Context, req *repository.UserHoldPosReq) (repository.HoldPosReply, error) {
	return uc.positionRepo.UserHoldPos(ctx, req)
}

// UserPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) UserPos(ctx context.Context, req *repository.SwapParam) ([]repository.PosSwap, error) {
	return uc.positionRepo.UserPos(ctx, req)
}

// OpenLongPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) OpenLongPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	userType := accountSettleParam.GetUserType()
	marginMode := accountSettleParam.GetMarginMode()
	leverage := accountSettleParam.GetLeverage()
	amount := accountSettleParam.GetAmount()
	price := accountSettleParam.GetPrice()
	base, quote := accountSettleParam.GetBaseQuote()
	unfrozenMargin := accountSettleParam.GetUnfrozenMargin()
	fee := accountSettleParam.GetFee()

	contractCode := util.ContractCode(base, quote)

	// 新建仓位
	if userAsset.LongPos.PosStatus == domain.PosStatusNone || userAsset.LongPos.PosStatus == domain.PosStatusEnd { // 新开仓分配持仓Id和首次开仓时间
		userAsset.LongPos.NewPos(userAsset.UID, userType, int32(marginMode), leverage)
		// 设置合约代码、方向、币种
		userAsset.LongPos.PosSide = domain.LongPos
		userAsset.LongPos.ContractCode = contractCode
		userAsset.LongPos.Currency = quote
	}

	// 计算开仓均价
	userAsset.LongPos.OpenPriceAvg = userAsset.LongPos.CalcOpenPriceAvg(amount, price)
	// 更新仓位数
	userAsset.LongPos.Pos = userAsset.LongPos.Pos.Add(amount)
	// 更新可平仓位
	userAsset.LongPos.PosAvailable = userAsset.LongPos.PosAvailable.Add(amount)

	// 减少冻结的保证金
	userAsset.DecrFrozen(contractCode, unfrozenMargin)

	// Todo 余额变更逻辑
	uc.BalanceAdd(fee.Neg(), userAsset, priceRepo, accountSettleParam)

	// 计算仓位保证金
	posMargin := userAsset.LongPos.CalcPosMargin(amount, price, fee)
	// 逐仓处理保证金
	if userAsset.LongPos.Isolated() {
		userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(posMargin)
	}

	ctx := context.Background()
	err := uc.cacheRepo.UpdateLongPos(ctx, contractCode, userAsset)
	if err != nil {
		return err
	}

	return nil
}

// OpenShortPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) OpenShortPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	userType := accountSettleParam.GetUserType()
	marginMode := accountSettleParam.GetMarginMode()
	leverage := accountSettleParam.GetLeverage()
	amount := accountSettleParam.GetAmount()
	price := accountSettleParam.GetPrice()
	base, quote := accountSettleParam.GetBaseQuote()
	unfrozenMargin := accountSettleParam.GetUnfrozenMargin()
	fee := accountSettleParam.GetFee()

	contractCode := util.ContractCode(base, quote)

	// 新建仓位
	if userAsset.ShortPos.PosStatus == domain.PosStatusNone || userAsset.ShortPos.PosStatus == domain.PosStatusEnd {
		userAsset.ShortPos.NewPos(userAsset.UID, userType, int32(marginMode), leverage)
		// 设置合约代码、方向、币种
		userAsset.ShortPos.PosSide = domain.ShortPos
		userAsset.ShortPos.ContractCode = contractCode
		userAsset.ShortPos.Currency = quote
	}

	// 计算开仓均价
	userAsset.ShortPos.OpenPriceAvg = userAsset.ShortPos.CalcOpenPriceAvg(amount, price)
	// 更新仓位数
	userAsset.ShortPos.Pos = userAsset.ShortPos.Pos.Add(amount)
	// 更新可平仓位
	userAsset.ShortPos.PosAvailable = userAsset.ShortPos.PosAvailable.Add(amount)

	// 减少冻结的保证金
	userAsset.DecrFrozen(contractCode, unfrozenMargin)

	// 余额变更逻辑
	uc.BalanceAdd(fee, userAsset, priceRepo, accountSettleParam)

	// 计算仓位保证金
	posMargin := userAsset.ShortPos.CalcPosMargin(amount, price, fee)
	//  逐仓处理保证金
	if userAsset.ShortPos.Isolated() {
		userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Add(posMargin)
	}

	ctx := context.Background()
	err := uc.cacheRepo.UpdateShortPos(ctx, contractCode, userAsset)
	if err != nil {
		return err
	}

	return nil
}

// OpenBothPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) OpenBothPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	// TODO: 实现开单向持仓逻辑
	return nil
}

// CloseLongPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) CloseLongPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	price := accountSettleParam.GetPrice()
	amount := accountSettleParam.GetAmount()
	fee := accountSettleParam.GetFee()
	uid := accountSettleParam.GetUID()
	base, quote := accountSettleParam.GetBaseQuote()

	// 体验金标志与拉平 ?
	// haveTrial := 0
	// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
	// 	haveTrial = 1
	// 	// 保证金小于体验金保证金时候需要强制拉平（仅可能会出现在纯体验金仓位）
	// 	if asset.LongPos.IsolatedMargin.LessThan(asset.LongPos.TrialMargin) {
	// 		asset.LongPos.TrialMargin = asset.LongPos.IsolatedMargin
	// 	}
	// }

	// 计算平仓盈亏，并更新仓位数量
	profitReal := userAsset.LongPos.CalcProfitReal(price, amount)
	userAsset.LongPos.Pos = userAsset.LongPos.Pos.Sub(amount)
	// compareProfitReal = profitReal
	// originPosAmount := asset.LongPos.Pos
	// originPosIsolateMargin := asset.LongPos.IsolatedMargin

	// 检查仓位和杠杆是否合法
	if userAsset.LongPos.Pos.Sign() < 0 {
		logrus.Info(0, "251111 pos is negative after closing long")
		return errors.New("pos is negative after closing long")
	}

	if userAsset.LongPos.Leverage < 1 {
		logrus.Info(0, "CloseLongPos 251114 long pos is leverage < 1", fmt.Sprintf("%+v", userAsset.LongPos))
		return errors.New("251114 long pos is leverage < 1")
	}

	// 更新平仓盈亏
	userAsset.LongPos.ProfitReal = userAsset.LongPos.ProfitReal.Add(profitReal)

	billProfitReal := profitReal

	//skipTrialDiscount := true
	// 亏钱先扣体检金?
	//if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) && billProfitReal.LessThan(decimal.Zero) {
	// ...
	//}

	// 用盈亏更新真金白银
	if !billProfitReal.IsZero() {
		uc.BalanceAdd(billProfitReal, userAsset, priceRepo, accountSettleParam)
		// Todo profitBillList := uc.BalanceAdd(billProfitReal, userAsset, priceRepo, accountSettleParam)
		// reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)
	}

	// 再扣手续费
	uc.BalanceAdd(fee.Neg(), userAsset, priceRepo, accountSettleParam)
	// Todo profitBillList := uc.BalanceAdd(billProfitReal, userAsset, priceRepo, accountSettleParam)
	// reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)

	// 手续费明细?
	// feeDetails := make([]payload.FeeDetail, 0)
	// for _, detail := range feeBillList.AssetLogs {
	// 	feeDetails = append(feeDetails, payload.FeeDetail{
	// 		Currency: detail.Currency,
	// 		Amount:   detail.Amount,
	// 		Price:    pCache.SpotURate(detail.Currency),
	// 	})
	// }

	// 暗成交?
	// 如果是暗成交的话, 生成的委托单没有进深度(撮合没有调用lock接口), 此时平仓后需要扣减 PosAvailable
	// 非暗成交时, 委托单进深度(撮合调用lock接口, 此时 PosAvailable 已经扣除了平仓的数量), 因此不用重复扣减 PosAvailable
	// if slf.TradeType == domain.TradeTypeDirect {
	// 	if asset.LongPos.PosAvailable.LessThan(slf.Amount) {
	// 		log.Printf("dark deal close long pos pos available less than slf.amount uid:%s PosAvailable:%s slf.amount:%s",
	// 			slf.UID, asset.LongPos.PosAvailable, slf.Amount)
	// 		return reply, domain.Code251119, errors.New("dark deal close long pos pos available less than slf.amount")
	// 	}
	// 	asset.LongPos.PosAvailable = asset.LongPos.PosAvailable.Sub(slf.Amount)
	// }

	// Todo 清算类型, 要让James加?
	// asset.LongPos.LiquidationType = trade.LiquidationType

	// 处理仓位保证金
	closePosMargin := decimal.Zero
	if userAsset.LongPos.Isolated() {
		// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
		closePosMargin = amount.Div(userAsset.LongPos.Pos).Mul(userAsset.LongPos.IsolatedMargin).Truncate(domain.CurrencyPrecision)

		subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin.Sub(fee))

		// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
		// 	asset.LongPos.TrialMargin = asset.LongPos.TrialMargin.Add(subDeltaAmount)
		// }

		// pos.IsolatedMargin 不可能小于0，subMargin 最大为当前仓位保证金数量
		userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(subDeltaAmount)

		if userAsset.LongPos.LiquidationType != domain.LiquidationTypeBurst {
			// 平仓穿仓逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
			if userAsset.LongPos.IsolatedMargin.Sign() < 0 {
				bankruptAmount := userAsset.LongPos.IsolatedMargin.Abs() // 这个补贴就是保证金正数?
				// Todo 穿仓记日志slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)

				// 逐仓保证金加穿仓补贴的费用
				userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(bankruptAmount)
				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)

				userAsset.LongPos.Subsidy = userAsset.LongPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close isolated long pos the balance of user drop below zero, value:%s uid:%s", bankruptAmount, uid)
			}
		}
	} else {
		closePosMargin = price.Mul(amount).Div(decimal.NewFromInt(int64(userAsset.LongPos.Leverage))).Truncate(domain.CurrencyPrecision)

		// 只有盈亏为负数时候, 从保证金上扣 ?
		//if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
		//	subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
		//	asset.LongPos.TrialMargin = decimal.Max(decimal.NewFromInt(0), asset.LongPos.TrialMargin.Add(subDeltaAmount))
		//}

		// 全仓非爆仓情况, 平仓后仓位保证金为负数时, 平台补贴
		if userAsset.LongPos.LiquidationType != domain.LiquidationTypeBurst {
			totalBalance := userAsset.CBalance(quote)
			rate := decimal.NewFromInt(1)
			if userAsset.AssetMode == domain.AssetMode { // 缺定义
				var err error = nil
				totalBalance, err = userAsset.TotalJoinBalance(priceRepo) // 缺方法
				if err != nil {
					log.Println("CloseLongPos close", err)
					return err
				}
				rate = priceRepo.SpotURate(ctx, quote)
				if rate.IsZero() {
					log.Println("CloseLongPos close rate is zero")
					return err // todo Code252408 由于已经联调下次修改需要更新
				}
			}

			// 全仓保证金中不包含体验金保证金，所以穿仓需要用体验金的保证金填补一次
			//if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) && totalBalance.Sign() < 0 {
			//	if totalBalance.Abs().LessThanOrEqual(asset.LongPos.TrialMargin.Mul(rate)) {
			//		totalBalance = totalBalance.Add(totalBalance.Neg())
			//		asset.LongPos.TrialMargin = asset.LongPos.TrialMargin.Add(totalBalance.Div(rate))
			//	} else {
			//		totalBalance = totalBalance.Add(asset.LongPos.TrialMargin.Mul(rate))
			//		asset.LongPos.TrialMargin = decimal.Zero
			//	}
			//}

			if totalBalance.Sign() < 0 {
				bankruptAmount := totalBalance.Div(rate).Abs()
				//Todo 穿仓的流水日志 slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)
				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)
				userAsset.LongPos.Subsidy = userAsset.LongPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close cross long pos the balance of user drop below zero, value:%s uid:%s", bankruptAmount, uid)
			}
		}

	}

	// 平仓后如果仓位归0, 生成历史持仓记录
	if userAsset.LongPos.Pos.Sign() <= 0 {
		//reply.ClearPos = asset.LongPos
		userAsset.LongPos.Clear() // 清空现有持仓
	}

	// 更新仓位缓存
	contractCode := util.ContractCode(base, quote)
	ctx := context.Background()
	err := uc.cacheRepo.UpdateLongPos(ctx, contractCode, userAsset)
	if err != nil {
		return err
	}

	// 撤单?
	// go func() {
	// 	// 平仓后亏损, 账户余额不够冻结金额, 进行撤单操作
	// 	totalBalance := asset.CBalance(trade.FeeCurrency)
	// 	if asset.AssetMode == domain.AssetMode {
	// 		totalBalance, err = asset.TotalJoinBalance(pCache)
	// 		if err != nil {
	// 			log.Println("CloseShortPos revoke", err)
	// 		}
	// 	}
	// 	if totalBalance.LessThan(slf.FrozenTotal(asset, slf.Quote)) {
	// 		match.Service.ConditionCancel(asset.ShortPos.UID, "", "", domain.Open, 0, 0, int32(domain.CancelTypeClosePosLoss), 1)
	// 	}
	// 	// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
	// 	if asset.ShortPos.Pos.Sign() <= 0 {
	// 		RemoveRivalScore(asset.ShortPos.UID, asset.ShortPos.ContractCode, asset.ShortPos.PosSide, false) // 删除对手方评分
	// 		match.Service.ConditionCancel(asset.ShortPos.UID, slf.Base, slf.Quote, domain.Close, domain.Buy, asset.ShortPos.MarginMode, int32(domain.CancelTypeClosePosAll), 1)
	// 	}
	// }()

	// 账本记录?
	// go func() {
	// 	if trade.UserType == domain.UserTypePlatformRobot {
	// 		return
	// 	}
	// 	balance, err := GetCurrencyTotalBalance(trade.UID, bFeeParam.Currency)
	// 	if err != nil {
	// 		logrus.Errorf("CloseShortPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
	// 		return
	// 	}
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.TRADE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, billProfitReal, balance.Add(trade.Fee))
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	// }()

	// 返回日志?
	// reply.LogPos = slf.NewLogPosSync(asset.ShortPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
	// 	trade.PosSide, slf.Amount, profitReal)
	// reply.Reply = payload.Reply{
	// 	UID:          asset.UID,
	// 	PosId:        asset.ShortPos.PosId,
	// 	Pos:          asset.ShortPos.Pos,
	// 	OpenPriceAvg: asset.ShortPos.OpenPriceAvg,
	// 	PosSide:      asset.ShortPos.PosSide,
	// 	ProfitReal:   profitReal,
	// 	FeeDetail:    feeDetails,
	// 	HaveTrial:    haveTrial,
	// }

	return nil
}

// CloseShortPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) CloseShortPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	// TODO: 实现平空仓逻辑
	return nil
}

// CloseBothPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) CloseBothPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	// TODO: 实现平单向持仓逻辑
	return nil
}

// BalanceAdd 处理余额变更逻辑
func (uc *PositionUseCase) BalanceAdd(amount decimal.Decimal, userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	// amount = 0 啥也不干
	if amount.IsZero() {
		return nil
	}

	// amount 大于0 (得加钱)
	_, quote := accountSettleParam.GetBaseQuote()
	if amount.Sign() > 0 {
		userAsset.AddBalance(quote, amount)
		return nil
	}

	// amount 负数当然扣他钱
	if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
		//if !skipTrial { 跳过体验金
		userAsset.AddBalance(quote, amount)
	} else { // 多币种扣减?

	}

	return nil
}
